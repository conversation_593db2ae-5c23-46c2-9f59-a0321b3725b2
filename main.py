from utils import download_video
from exAudio import *
from speech2text import *
import os

# Main文件是作者用来测试的，请运行window.py

try:
    av = input("请输入BV号：")

    # 确保BV号格式正确
    if not av.startswith("BV"):
        av = "BV" + av

    print(f"处理视频: {av}")

    # 检查视频目录是否存在
    video_dir = f"bilibili_video/{av}"
    if not os.path.exists(video_dir):
        print(f"视频目录不存在: {video_dir}")
        print("请先下载视频或检查BV号是否正确")
        exit(1)

    # 检查目录下是否有视频文件
    video_files = [f for f in os.listdir(video_dir) if f.lower().endswith(('.mp4', '.flv'))]
    if not video_files:
        print(f"目录 {video_dir} 下没有找到视频文件")
        print("请检查视频是否下载完成")
        exit(1)

    print(f"找到视频文件: {video_files}")

    filename = download_video(av[2:] if av.startswith("BV") else av)
    foldername = process_audio_split(filename)

    load_whisper("small")
    run_analysis(foldername, prompt="以下是普通话的句子。")
    output_path = f"outputs/{foldername}.txt"
    print("转换完成！", output_path)

except FileNotFoundError as e:
    print(f"文件未找到错误: {e}")
    print("请检查:")
    print("1. BV号是否正确")
    print("2. 视频是否已下载到 bilibili_video 目录")
    print("3. 视频文件是否完整")
except Exception as e:
    print(f"发生错误: {e}")
    print("请检查错误信息并重试")
