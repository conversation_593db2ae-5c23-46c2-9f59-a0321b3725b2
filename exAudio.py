from moviepy.editor import VideoFileClip
from pydub import AudioSegment
import os
import time
import subprocess

def check_video_integrity(file_path):
    """使用 FFmpeg 验证视频文件完整性"""
    result = subprocess.run(
        ['ffmpeg', '-v', 'error', '-i', file_path, '-f', 'null', '-'],
        stderr=subprocess.PIPE,
        text=True
    )
    if result.stderr:
        print(f"视频文件可能损坏: {file_path}")
        print(f"FFmpeg 错误信息: {result.stderr}")
        return False
    return True

def convert_flv_to_mp3(input_path, target_name=None, folder='bilibili_video'):
    import os
    # 如果 input_path 是文件夹，则查找视频文件
    if os.path.isdir(input_path):
        video_files = sorted([f for f in os.listdir(input_path) if f.lower().endswith(('.mp4', '.flv'))])
        if len(video_files) == 0:
            raise FileNotFoundError(f"目录下没有找到视频文件: {input_path}")
        # 优先选择最后一个文件（通常是完整的视频），如果只有一个就选择第一个
        input_path = os.path.join(input_path, video_files[-1])
        print(f"找到视频文件: {input_path}")
    # 如果 input_path 不是文件，尝试拼接
    elif not os.path.exists(input_path):
        input_path = os.path.join(folder, f"{input_path}.mp4")
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"视频文件不存在: {input_path}")
    if not check_video_integrity(input_path):
        raise ValueError(f"视频文件损坏: {input_path}")
    # 提取音频
    clip = VideoFileClip(input_path)
    audio = clip.audio
    os.makedirs("audio/conv", exist_ok=True)
    output_name = target_name if target_name else os.path.splitext(os.path.basename(input_path))[0]
    audio.write_audiofile(f"audio/conv/{output_name}.mp3")

def split_mp3(filename, folder_name, slice_length=45000, target_folder="audio/slice"):
    audio = AudioSegment.from_mp3(filename)
    total_slices = (len(audio)+ slice_length - 1) // slice_length
    target_dir = os.path.join(target_folder, folder_name)
    os.makedirs(target_dir, exist_ok=True)
    for i in range(total_slices):
        start = i * slice_length
        end = start + slice_length
        slice_audio = audio[start:end]
        slice_path = os.path.join(target_dir, f"{i+1}.mp3")
        slice_audio.export(slice_path, format="mp3")
        print(f"Slice {i+1} saved: {slice_path}")

def process_audio_split(bv_number):
    import time
    folder_name = time.strftime('%Y%m%d%H%M%S')
    video_folder = f"bilibili_video/{bv_number}"
    convert_flv_to_mp3(video_folder, target_name=folder_name)
    conv_mp3_path = f"audio/conv/{folder_name}.mp3"
    split_mp3(conv_mp3_path, folder_name)
    return folder_name

